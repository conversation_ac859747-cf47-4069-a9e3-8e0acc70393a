<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\AppUser;
use Illuminate\Support\Facades\Log;

class VipBalanceController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取VIP用户余额列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            Log::info('VipBalanceController: index方法被调用', [
                'request_all' => $request->all(),
                'path' => $request->path(),
                'method' => $request->method()
            ]);
            
            // 构建VIP用户查询
            $query = AppUser::where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->select([
                    'id',
                    'name',
                    'wechat_nickname',
                    'phone',
                    'email',
                    'avatar',
                    'wechat_avatar',
                    'balance',
                    'status',
                    'is_vip',
                    'vip_at',
                    'referrer_id',
                    'created_at',
                    'updated_at'
                ]);

            // 搜索条件
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('wechat_nickname', 'like', "%{$search}%")
                      ->orWhere('id', 'like', "%{$search}%");
                });
            }

            // 余额范围筛选
            if ($request->filled('min_balance')) {
                $query->where('balance', '>=', $request->min_balance);
            }
            if ($request->filled('max_balance')) {
                $query->where('balance', '<=', $request->max_balance);
            }

            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // 注册时间筛选
            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->start_date);
            }
            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
            }

            // VIP时间筛选
            if ($request->filled('vip_start_date')) {
                $query->where('vip_at', '>=', $request->vip_start_date);
            }
            if ($request->filled('vip_end_date')) {
                $query->where('vip_at', '<=', $request->vip_end_date . ' 23:59:59');
            }

            // 排序
            $orderBy = $request->input('order_by', 'id');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);

            // 分页参数
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 15);
            $perPage = $request->input('per_page', $limit); // 兼容两种参数名

            // 执行查询
            $users = $query->paginate($perPage, ['*'], 'page', $page);

            // 处理用户数据
            $users->getCollection()->transform(function ($user) {
                // 获取推荐人信息
                if (!empty($user->referrer_id)) {
                    $referrer = AppUser::select('id', 'name', 'wechat_nickname')
                        ->where('id', $user->referrer_id)
                        ->first();
                    if ($referrer) {
                        $user->referrer_name = $referrer->name ?: $referrer->wechat_nickname ?: '用户'.$referrer->id;
                    } else {
                        $user->referrer_name = '未知用户';
                    }
                } else {
                    $user->referrer_name = '点点够';
                }

                // 获取团队VIP人数
                $user->team_vip_count = AppUser::where('referrer_id', $user->id)
                    ->where('is_vip', 1)
                    ->whereNotNull('vip_at')
                    ->count();

                // 获取直推VIP人数
                $user->direct_vip_count = AppUser::where('referrer_id', $user->id)
                    ->where('is_vip', 1)
                    ->whereNotNull('vip_at')
                    ->count();

                // 格式化显示名称
                $user->display_name = $user->name ?: $user->wechat_nickname ?: '用户'.$user->id;

                // 格式化余额
                $user->balance = number_format($user->balance, 2);

                // 格式化时间
                $user->vip_at_formatted = $user->vip_at ? date('Y-m-d H:i:s', strtotime($user->vip_at)) : null;
                $user->created_at_formatted = $user->created_at ? date('Y-m-d H:i:s', strtotime($user->created_at)) : null;

                return $user;
            });

            // 计算统计数据
            $totalBalanceQuery = AppUser::where('is_vip', 1)->whereNotNull('vip_at');
            $totalBalance = $totalBalanceQuery->sum('balance');
            $totalVipUsers = $totalBalanceQuery->count();
            $avgBalance = $totalVipUsers > 0 ? $totalBalance / $totalVipUsers : 0;

            // 今日余额变动
            $todayChanges = DB::table('app_user_balance_logs')
                ->join('app_users', 'app_user_balance_logs.user_id', '=', 'app_users.id')
                ->where('app_users.is_vip', 1)
                ->whereNotNull('app_users.vip_at')
                ->whereDate('app_user_balance_logs.created_at', today())
                ->sum('app_user_balance_logs.amount');

            // 返回数据
            return $this->success([
                'users' => $users->items(),
                'pagination' => [
                    'current_page' => $users->currentPage(),
                    'last_page' => $users->lastPage(),
                    'per_page' => $users->perPage(),
                    'total' => $users->total(),
                    'from' => $users->firstItem(),
                    'to' => $users->lastItem(),
                ],
                'statistics' => [
                    'total_balance' => number_format($totalBalance, 2),
                    'total_vip_users' => $totalVipUsers,
                    'avg_balance' => number_format($avgBalance, 2),
                    'today_changes' => number_format($todayChanges, 2),
                ]
            ], '获取VIP余额列表成功');

        } catch (\Exception $e) {
            Log::error('获取VIP余额列表失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return $this->error('获取VIP余额列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 调整VIP用户余额
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function adjustBalance(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:app_users,id',
            'type' => 'required|in:add,subtract,set',
            'amount' => 'required|numeric|min:0',
            'reason' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', 422, $validator->errors());
        }

        try {
            DB::beginTransaction();

            // 查找用户
            $user = AppUser::where('id', $request->user_id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->first();

            if (!$user) {
                return $this->error('VIP用户不存在', 404);
            }

            // 记录调整前余额
            $balanceBefore = $user->balance;
            $adjustAmount = $request->amount;
            $balanceAfter = $balanceBefore;

            // 根据调整类型计算新余额
            switch ($request->type) {
                case 'add':
                    $balanceAfter = $balanceBefore + $adjustAmount;
                    $actualAmount = $adjustAmount;
                    break;
                case 'subtract':
                    $balanceAfter = max(0, $balanceBefore - $adjustAmount);
                    $actualAmount = -($balanceBefore - $balanceAfter);
                    break;
                case 'set':
                    $balanceAfter = $adjustAmount;
                    $actualAmount = $balanceAfter - $balanceBefore;
                    break;
                default:
                    return $this->error('无效的调整类型', 400);
            }

            // 更新用户余额
            $user->balance = $balanceAfter;
            $user->save();

            // 记录余额变动日志
            DB::table('app_user_balance_logs')->insert([
                'user_id' => $user->id,
                'type' => 'admin_adjust',
                'amount' => $actualAmount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'reason' => $request->reason,
                'operator_id' => auth()->id() ?? 1, // 如果没有认证用户，默认使用ID 1
                'operator_name' => auth()->user()->name ?? 'System',
                'operator_type' => 'admin',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::commit();

            return $this->success([
                'user_id' => $user->id,
                'user_name' => $user->name ?: $user->wechat_nickname ?: '用户'.$user->id,
                'balance_before' => number_format($balanceBefore, 2),
                'balance_after' => number_format($balanceAfter, 2),
                'amount' => number_format($actualAmount, 2),
                'type' => $request->type,
                'reason' => $request->reason,
            ], '余额调整成功');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('VIP用户余额调整失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return $this->error('余额调整失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取用户余额变动记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function balanceLogs(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:app_users,id',
        ]);

        if ($validator->fails()) {
            return $this->error('参数验证失败', 422, $validator->errors());
        }

        try {
            // 查找用户
            $user = AppUser::where('id', $request->user_id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->first();

            if (!$user) {
                return $this->error('VIP用户不存在', 404);
            }

            // 构建查询
            $query = DB::table('app_user_balance_logs')
                ->where('user_id', $request->user_id)
                ->orderBy('created_at', 'desc');

            // 按类型筛选
            if ($request->filled('type')) {
                $query->where('type', $request->type);
            }

            // 按日期范围筛选
            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->start_date);
            }
            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
            }

            // 分页
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 15);
            $offset = ($page - 1) * $perPage;

            $total = $query->count();
            $logs = $query->offset($offset)->limit($perPage)->get();

            // 格式化数据
            $formattedLogs = $logs->map(function($log) {
                return [
                    'id' => $log->id,
                    'type' => $log->type,
                    'type_name' => $this->getBalanceTypeText($log->type),
                    'amount' => number_format($log->amount, 2),
                    'balance_before' => number_format($log->balance_before, 2),
                    'balance_after' => number_format($log->balance_after, 2),
                    'reason' => $log->reason,
                    'operator_name' => $log->operator_name ?: '系统',
                    'operator_type' => $log->operator_type ?: 'system',
                    'created_at' => $log->created_at,
                    'created_at_formatted' => date('Y-m-d H:i:s', strtotime($log->created_at)),
                ];
            });

            return $this->success([
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name ?: $user->wechat_nickname ?: '用户'.$user->id,
                    'current_balance' => number_format($user->balance, 2),
                ],
                'logs' => $formattedLogs,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total),
                ]
            ], '获取余额变动记录成功');

        } catch (\Exception $e) {
            Log::error('获取用户余额变动记录失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return $this->error('获取余额变动记录失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取VIP用户余额统计
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics(Request $request)
    {
        try {
            // 基础统计
            $totalBalanceQuery = AppUser::where('is_vip', 1)->whereNotNull('vip_at');
            $totalBalance = $totalBalanceQuery->sum('balance');
            $totalVipUsers = $totalBalanceQuery->count();
            $avgBalance = $totalVipUsers > 0 ? $totalBalance / $totalVipUsers : 0;

            // 今日余额变动
            $todayChanges = DB::table('app_user_balance_logs')
                ->join('app_users', 'app_user_balance_logs.user_id', '=', 'app_users.id')
                ->where('app_users.is_vip', 1)
                ->whereNotNull('app_users.vip_at')
                ->whereDate('app_user_balance_logs.created_at', today())
                ->sum('app_user_balance_logs.amount');

            // 本月余额变动
            $monthlyChanges = DB::table('app_user_balance_logs')
                ->join('app_users', 'app_user_balance_logs.user_id', '=', 'app_users.id')
                ->where('app_users.is_vip', 1)
                ->whereNotNull('app_users.vip_at')
                ->whereMonth('app_user_balance_logs.created_at', now()->month)
                ->whereYear('app_user_balance_logs.created_at', now()->year)
                ->sum('app_user_balance_logs.amount');

            // 余额区间分布
            $balanceDistribution = [
                '0-100' => AppUser::where('is_vip', 1)->whereNotNull('vip_at')->whereBetween('balance', [0, 100])->count(),
                '100-500' => AppUser::where('is_vip', 1)->whereNotNull('vip_at')->whereBetween('balance', [100, 500])->count(),
                '500-1000' => AppUser::where('is_vip', 1)->whereNotNull('vip_at')->whereBetween('balance', [500, 1000])->count(),
                '1000-5000' => AppUser::where('is_vip', 1)->whereNotNull('vip_at')->whereBetween('balance', [1000, 5000])->count(),
                '5000+' => AppUser::where('is_vip', 1)->whereNotNull('vip_at')->where('balance', '>', 5000)->count(),
            ];

            return $this->success([
                'total_balance' => number_format($totalBalance, 2),
                'total_vip_users' => $totalVipUsers,
                'avg_balance' => number_format($avgBalance, 2),
                'today_changes' => number_format($todayChanges, 2),
                'monthly_changes' => number_format($monthlyChanges, 2),
                'balance_distribution' => $balanceDistribution,
            ], '获取统计数据成功');

        } catch (\Exception $e) {
            Log::error('获取VIP余额统计失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return $this->error('获取统计数据失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取余额变动类型文本
     *
     * @param string $type
     * @return string
     */
    private function getBalanceTypeText($type)
    {
        $types = [
            'admin_adjust' => '管理员调整',
            'recharge' => '充值',
            'consume' => '消费',
            'refund' => '退款',
            'dividend' => '分红',
            'withdraw' => '提现',
            'commission' => '佣金',
            'reward' => '奖励',
            'penalty' => '扣款',
            'transfer_in' => '转入',
            'transfer_out' => '转出',
        ];

        return $types[$type] ?? $type;
    }
} 