<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\Api\V1\SystemController;
use App\Http\Controllers\Admin\Api\V1\BranchDividendConfigController;
use App\Http\Controllers\Admin\Api\V1\BranchOrganizationController;
use App\Http\Controllers\Admin\Api\V1\BranchMenuController;
use App\Http\Controllers\Admin\Api\V1\NotificationController;
use App\Http\Controllers\Admin\Api\V1\VoiceNotificationController;
use App\Http\Controllers\Admin\Api\V1\ScheduledTaskController;
use App\Http\Controllers\Admin\Api\V1\BranchManagementController;

/*
|--------------------------------------------------------------------------
| 管理后台API路由 (V1版本)
|--------------------------------------------------------------------------
|
| 这里定义所有管理后台的RESTful API路由
| 统一使用 /api/admin/v1 前缀
|
*/

// 测试路由 - 不需要认证
Route::get('test', function () {
    return response()->json([
        'code' => 200,
        'message' => 'V1 API 工作正常 (from admin_api_v1.php)',
        'data' => [
            'timestamp' => now(),
            'version' => 'v1',
            'source' => 'admin_api_v1.php',
            'route_registered' => true
        ]
    ]);
});

// 测试分支菜单API - 不需要认证
Route::get('test-branch-menus', function () {
    try {
        $controller = new \App\Http\Controllers\Api\Admin\V1\BranchMenuController();
        $request = new \Illuminate\Http\Request();
        $response = $controller->getMenuTree($request);
        return $response;
    } catch (\Exception $e) {
        return response()->json([
            'code' => 500,
            'message' => '测试失败: ' . $e->getMessage(),
            'data' => null
        ]);
    }
});

// 测试分类API - 不需要认证
Route::get('test-categories', function () {
    try {
        $categories = \App\Models\Legacy\LegacyGoodsCategory::take(5)->get();
        return response()->json([
            'code' => 0,
            'message' => '分类测试成功',
            'data' => [
                'count' => $categories->count(),
                'categories' => $categories->toArray()
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'code' => 1,
            'message' => '分类测试失败: ' . $e->getMessage(),
            'data' => null
        ]);
    }
});

// 测试分类控制器 - 不需要认证
Route::get('test-categories-controller', function (\Illuminate\Http\Request $request) {
    try {
        $controller = new \App\Http\Controllers\Admin\Api\V1\MallCategoryController();
        $response = $controller->list($request);
        return $response;
    } catch (\Exception $e) {
        return response()->json([
            'code' => 1,
            'message' => '控制器测试失败: ' . $e->getMessage(),
            'data' => null
        ]);
    }
});

// 测试设备路由 - 直接返回JSON
Route::get('app-users/{id}/devices-test', function ($id) {
    return response()->json([
        'code' => 200,
        'message' => '设备路由测试成功',
        'data' => [
            'user_id' => $id,
            'devices' => [],
            'test' => true
        ]
    ]);
});

// 测试AdminController路由
Route::get('admins-test', function () {
    try {
        $controller = new \App\Http\Controllers\Admin\Api\V1\AdminController();
        return response()->json([
            'code' => 200,
            'message' => 'AdminController测试成功',
            'data' => [
                'controller_exists' => true,
                'timestamp' => now()
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'code' => 500,
            'message' => 'AdminController测试失败: ' . $e->getMessage(),
            'data' => null
        ]);
    }
});

// 测试路由 - 验证API是否正常工作
Route::get('/test-birthday', function () {
    return response()->json([
        'code' => 200,
        'message' => '生日API测试成功',
        'data' => [
            'timestamp' => now(),
            'route' => 'admin_api_v1.php'
        ]
    ]);
});

// 菜单API - V1版本 - 不需要认证
Route::prefix('menus')->group(function () {
    Route::get('/', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'index']);
    Route::get('/list', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'index']);
    Route::post('/', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'store']);
    Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'show']);
    Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'update']);
    Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'destroy']);
    
    // 新增的菜单管理API
    Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'updateStatus']);
    Route::put('/sort', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'updateSort']);
    Route::delete('/batch-delete', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'batchDelete']);
    Route::post('/initialize-default', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'initializeDefault']);
});

// 管理员认证相关API
Route::prefix('auth')->group(function () {
    Route::post('login', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'login']);
    Route::post('logout', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'logout'])->middleware('auth:sanctum');
    Route::get('me', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'me'])->middleware('auth:sanctum');
    Route::post('refresh-token', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'refreshToken'])->middleware('auth:sanctum');
    Route::get('check-token', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'checkToken'])->middleware('auth:sanctum');
    
    // 个人资料管理
    Route::post('update-profile', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'updateProfile'])->middleware('auth:sanctum');
    Route::post('change-password', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'changePassword'])->middleware('auth:sanctum');
    
    // 微信绑定相关API - 需要认证的路由
    Route::get('wechat/bind-url', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'getWechatBindUrl']);
    Route::get('wechat/bind-status', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'checkWechatBindStatus']);
    Route::get('wechat/callback', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'wechatBindCallback']);
    
    // 需要认证的微信相关API
    Route::post('wechat/bind-after-login', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'bindWechatAfterLogin'])->middleware('auth:sanctum');
    Route::post('wechat/unbind', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'unbindWechat'])->middleware('auth:sanctum');
    Route::post('wechat/toggle', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'toggleWechatLogin'])->middleware('auth:sanctum');
    
    // 微信配置获取 - 不需要认证
    Route::get('wechat/config', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'getWechatConfig']);
    
    // 微信登录相关
    Route::get('wechat/login-url', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'getWechatLoginUrl']);
    Route::get('wechat/login-status', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'checkWechatLoginStatus']);
});

// 分公司分红配置管理 - V1版本API（不需要认证，修复"无效的令牌或已过期"问题）
Route::prefix('branch-dividend-configs')->group(function () {
    // 统计数据 - 必须在动态路由之前
    Route::get('statistics', [BranchDividendConfigController::class, 'statistics']);
    
    // 获取分支机构选项
    Route::get('branch-options', [BranchDividendConfigController::class, 'getBranchOptions']);
    
    // 批量操作
    Route::post('batch', [BranchDividendConfigController::class, 'batch']);
    
    // 导出功能
    Route::post('export', [BranchDividendConfigController::class, 'export']);
    
    // 基础CRUD
    Route::get('/', [BranchDividendConfigController::class, 'index']);
    Route::post('/', [BranchDividendConfigController::class, 'store']);
    Route::get('{id}', [BranchDividendConfigController::class, 'show']);
    Route::put('{id}', [BranchDividendConfigController::class, 'update']);
    Route::delete('{id}', [BranchDividendConfigController::class, 'destroy']);
    
    // 状态管理
    Route::put('{id}/status', [BranchDividendConfigController::class, 'updateStatus']);
    
    // 预览功能
    Route::get('{id}/preview', [BranchDividendConfigController::class, 'preview']);
});

// 微信第三方平台管理 - V1版本API（不需要认证，修复"获取第三方授权公众号失败"问题）
Route::prefix('wechat-third-party-platforms')->group(function () {
    Route::get('/', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'index']);
    Route::post('/', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'store']);
    Route::get('/options', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'options']);
    Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'show']);
    Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'update']);
    Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'destroy']);
    Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'updateStatus']);
    Route::post('/{id}/generate-auth-url', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'generateAuthUrl']);
    Route::post('/{id}/auth-callback', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'handleAuthCallback']);
    Route::get('/{id}/authorized-accounts', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'getAuthorizedAccounts']);
    Route::post('/{id}/refresh-token/{accountId}', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'refreshAuthorizerToken']);
});

// 微信第三方平台单一配置管理 - V1版本API（不需要认证）
Route::prefix('wechat-third-party-platform')->group(function () {
    Route::get('/config', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'getConfig']);
    Route::post('/config', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'saveConfig']);
    Route::post('/test-method', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'testMethod']);
    Route::post('/generate-auth-url', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'generateAuthUrl']);
    Route::get('/authorized-accounts', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'getAuthorizedAccounts']);
    Route::post('/refresh-token', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'refreshToken']);
    Route::post('/auth-callback', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'handleAuthCallback']);
    Route::post('/start-push-ticket', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'startPushTicket']);
    Route::get('/status', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'getPlatformStatus']);
    Route::get('/check-ticket-status', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'checkTicketStatus']);
    Route::post('/force-refresh-token', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'forceRefreshToken']);
    // 更新授权账号AppSecret
    Route::put('/authorized-accounts/{id}/app-secret', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'updateAppSecret']);
    // 更新所有公众号粉丝数
    Route::post('/update-subscriber-counts', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'updateSubscriberCounts']);
    // 获取公众号权限集
    Route::get('/account-permissions/{appid}', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'getAccountPermissions']);
});

// 需要认证和访问控制的管理后台API
Route::middleware(['auth:sanctum', 'admin.access'])->group(function () {
    // 仪表板统计 - V1版本API
    Route::get('dashboard/stats', [\App\Http\Controllers\Admin\Api\V1\DashboardController::class, 'stats']);
    Route::get('dashboard/charts', [\App\Http\Controllers\Admin\Api\V1\DashboardController::class, 'charts']);
    
    // 设备管理 - V1版本API
    Route::get('devices', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'index']);
    Route::get('devices/{id}', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'show']);
    Route::post('devices', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'store']);
    Route::put('devices/{id}', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'update']);
    Route::delete('devices/{id}', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'destroy']);
    Route::put('devices/{id}/status', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'status']);
    Route::post('devices/{id}/bind-user', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'bindUser']);
    Route::post('devices/{id}/unbind-user', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'unbindUser']);
    Route::post('devices/{id}/control', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'control']);
    Route::get('devices/{id}/logs', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'logs']);
    Route::get('device-types', [\App\Http\Controllers\Admin\Api\V1\DeviceController::class, 'deviceTypes']);
    
    // APP用户管理 - 需要认证和访问控制
    Route::get('app-users', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'index']);
    Route::get('app-users/{id}', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'show']);
    Route::post('app-users', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'store']);
    Route::put('app-users/{id}', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'update']);
    Route::delete('app-users/{id}', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'destroy']);
    Route::put('app-users/{id}/status', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'updateStatus']);
    Route::post('app-users/{id}/reset-password', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'resetPassword']);
    Route::get('app-users/{id}/orders', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'orders']);
    Route::get('app-users/{id}/devices', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'devices']);
    
    // 用户角色同步相关路由
    Route::post('app-users/sync-all-roles', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'syncAllRoles']);
    Route::post('app-users/{id}/sync-roles', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'syncUserRoles']);
    
    // 管理员管理 - 需要认证和访问控制
    Route::get('admins', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'index']);
    Route::get('admins/{id}', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'show']);
    Route::post('admins', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'store']);
    Route::put('admins/{id}', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'update']);
    Route::delete('admins/{id}', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'destroy']);
    Route::put('admins/{id}/status', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'updateStatus']);
    Route::post('admins/{id}/reset-password', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'resetPassword']);
    Route::get('admins/roles', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'roles']);
    
    // 业务员管理 - V1版本API
    Route::get('salesmen', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'index']);
    Route::get('salesmen/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'show']);
    Route::post('salesmen', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'store']);
    Route::put('salesmen/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'update']);
    Route::delete('salesmen/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'destroy']);
    Route::get('salesmen/{id}/stats', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'stats']);
    Route::get('salesmen/{id}/team', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'team']);
    Route::get('salesmen/managers', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'getManagers']);
    Route::get('salesmen/available-users', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'getAvailableUsers']);
    
    // 业务员销售记录管理 - V1版本API
    Route::prefix('salesman-sales')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\SalesmanSaleController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\SalesmanSaleController::class, 'store']);
        Route::get('/stats', [\App\Http\Controllers\Admin\Api\V1\SalesmanSaleController::class, 'stats']);
        Route::get('/device-sales-stats', [\App\Http\Controllers\Admin\Api\V1\SalesmanSaleController::class, 'deviceSalesStats']);
        Route::post('/sync-device-sales', [\App\Http\Controllers\Admin\Api\V1\SalesmanSaleController::class, 'syncDeviceSales']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanSaleController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanSaleController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanSaleController::class, 'destroy']);
    });
    
    // 业务员客户管理 - V1版本API
    Route::get('salesman-customers/stats', [\App\Http\Controllers\Admin\Api\V1\SalesmanCustomerController::class, 'stats']);
    Route::post('salesman-customers/batch-import', [\App\Http\Controllers\Admin\Api\V1\SalesmanCustomerController::class, 'batchImport']);
    Route::get('salesman-customers', [\App\Http\Controllers\Admin\Api\V1\SalesmanCustomerController::class, 'index']);
    Route::get('salesman-customers/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanCustomerController::class, 'show']);
    Route::post('salesman-customers', [\App\Http\Controllers\Admin\Api\V1\SalesmanCustomerController::class, 'store']);
    Route::put('salesman-customers/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanCustomerController::class, 'update']);
    Route::delete('salesman-customers/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanCustomerController::class, 'destroy']);
    
    // 业务员提成管理 - V1版本API
    Route::prefix('salesman-commissions')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\SalesmanCommissionController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\SalesmanCommissionController::class, 'store']);
    });
    

    
    // 分公司分红配置管理已移至不需要认证的区域
    
    // 微信公众号管理 - V1版本API
    Route::prefix('wechat-accounts')->group(function () {
        Route::get('/options', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'options']);
        Route::get('/subscriber-stats', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'getSubscriberStats']);
        Route::post('/batch-update-subscribers', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'batchUpdateSubscriberCount']);
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'store']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'destroy']);
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'updateStatus']);
        Route::post('/{id}/test-config', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'testConfig']);
        Route::put('/{id}/subscriber-count', [\App\Http\Controllers\Admin\Api\V1\WechatAccountController::class, 'updateSubscriberCount']);
    });

    // 微信第三方平台相关API已移至不需要认证的区域
    
    // 业务员同步管理 - V1版本API
    Route::post('salesman-sync/sync-app-users', [\App\Http\Controllers\Admin\Api\V1\SalesmanSyncController::class, 'syncAppUsers']);
    Route::get('salesman-sync/available-users', [\App\Http\Controllers\Admin\Api\V1\SalesmanSyncController::class, 'getAvailableUsers']);
    Route::post('salesman-sync/sync-single-user', [\App\Http\Controllers\Admin\Api\V1\SalesmanSyncController::class, 'syncSingleUser']);
    Route::post('salesman-sync/unsync-salesman', [\App\Http\Controllers\Admin\Api\V1\SalesmanSyncController::class, 'unsyncSalesman']);
    Route::get('salesman-sync/stats', [\App\Http\Controllers\Admin\Api\V1\SalesmanSyncController::class, 'getSyncStats']);
    Route::post('salesman-sync/batch-update', [\App\Http\Controllers\Admin\Api\V1\SalesmanSyncController::class, 'batchUpdate']);
    
    // 工程师管理 - V1版本API
    Route::get('installation-engineers', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'index']);
    Route::get('installation-engineers/{id}', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'show']);
    Route::post('installation-engineers', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'store']);
    Route::put('installation-engineers/{id}', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'update']);
    Route::delete('installation-engineers/{id}', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'destroy']);
    Route::put('installation-engineers/{id}/status', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'updateStatus']);
    Route::get('installation-engineers/{id}/installations', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'installations']);
    Route::get('installation-engineers/{id}/stats', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'stats']);
    Route::get('installation-engineers/regions', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'regions']);
    Route::get('installation-engineers/available', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'available']);
    Route::post('installation-engineers/sync-from-water-db', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'syncFromWaterDb']);
    Route::post('installation-engineers/update-installation-count', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'updateInstallationCount']);

    Route::post('installation-engineers/batch-operate', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'batchOperate']);
    Route::get('installation-engineers/export', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'export']);
    Route::post('installation-engineers/{id}/assign-work', [\App\Http\Controllers\Admin\Api\V1\InstallationEngineerController::class, 'assignWork']);
    
    // 首页导航管理 - V1版本API
    Route::prefix('home-navs')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\HomeNavController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\HomeNavController::class, 'store']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\HomeNavController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\HomeNavController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\HomeNavController::class, 'destroy']);
        Route::get('/active/list', [\App\Http\Controllers\Admin\Api\V1\HomeNavController::class, 'getActiveNavItems']);
        Route::get('/icons/list', [\App\Http\Controllers\Admin\Api\V1\HomeNavController::class, 'getVantIcons']);
    });

    // 产品分类管理 - V1版本API
    Route::prefix('categories')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'index']);
        Route::get('/all', [\App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'all']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'store']);
        Route::post('/upload-icon', [\App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'uploadIcon']);
        Route::post('/update-sort', [\App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'updateSort']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'destroy']);
    });
    
    // Banner管理 - V1版本API
    Route::prefix('banners')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'store']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'destroy']);
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'updateStatus']);
        Route::post('/update-sort', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'updateSort']);
    });
    
    // 取水点管理 - V1版本API
    Route::prefix('water-points')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'store']);
        Route::get('/statistics', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'statistics']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'destroy']);
        Route::post('/batch-delete', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'batchDestroy']);
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'updateStatus']);
        Route::put('/{id}/open-status', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'updateOpenStatus']);
    });
    
    // 语音通知管理 - V1版本API
    Route::prefix('voice-notifications')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'list']);
        Route::post('/played', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'markPlayed']);
        Route::post('/test', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'test']);
        Route::post('/batch-played', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'batchMarkPlayed']);
    });

    // 通知管理 - V1版本API
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::post('/', [NotificationController::class, 'store']);
        Route::get('/unread-count', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'unreadCount']);
        Route::get('/stats', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'stats']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'destroy']);
        Route::post('/{id}/read', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'markAsRead']);
        Route::post('/mark-all-read', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'markAllAsRead']);
        Route::post('/batch-delete', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'batchDelete']);
    });

    // 系统管理 - 权限管理
    Route::prefix('permissions')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\PermissionController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\PermissionController::class, 'store']);
        Route::get('/groups', [\App\Http\Controllers\Admin\Api\V1\PermissionController::class, 'groups']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\PermissionController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\PermissionController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\PermissionController::class, 'destroy']);
    });

    // 系统管理 - 角色管理
    Route::prefix('roles')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\RoleController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\RoleController::class, 'store']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\RoleController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\RoleController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\RoleController::class, 'destroy']);
        Route::put('/{id}/permissions', [\App\Http\Controllers\Admin\Api\V1\RoleController::class, 'updatePermissions']);
    });

    // 商城管理 - V1版本API
    // 官方商城管理
    Route::prefix('mall/official')->group(function () {
        Route::get('/dashboard', [\App\Http\Controllers\Admin\Api\V1\Mall\OfficialMallController::class, 'dashboard']);
        Route::get('/products', [\App\Http\Controllers\Admin\Api\V1\Mall\OfficialMallController::class, 'getProducts']);
        Route::get('/categories', [\App\Http\Controllers\Admin\Api\V1\Mall\OfficialMallController::class, 'getCategories']);
        Route::post('/categories', [\App\Http\Controllers\Admin\Api\V1\Mall\OfficialMallController::class, 'createCategory']);
        Route::put('/categories/{id}', [\App\Http\Controllers\Admin\Api\V1\Mall\OfficialMallController::class, 'updateCategory']);
        Route::delete('/categories/{id}', [\App\Http\Controllers\Admin\Api\V1\Mall\OfficialMallController::class, 'deleteCategory']);
        Route::put('/categories/{id}/status', [\App\Http\Controllers\Admin\Api\V1\Mall\OfficialMallController::class, 'updateCategoryStatus']);
        Route::get('/orders', [\App\Http\Controllers\Admin\Api\V1\Mall\OfficialMallController::class, 'getOrders']);
        Route::put('/products/{id}/status', [\App\Http\Controllers\Admin\Api\V1\Mall\OfficialMallController::class, 'updateProductStatus']);
        Route::put('/orders/{id}/status', [\App\Http\Controllers\Admin\Api\V1\Mall\OfficialMallController::class, 'updateOrderStatus']);
    });

    // 商户商城管理
    Route::prefix('mall/merchant')->group(function () {
        Route::get('/dashboard', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'dashboard']);
        Route::get('/merchants', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'getMerchants']);
        Route::get('/categories', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'getCategories']);
        Route::post('/categories', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'createCategory']);
        Route::put('/categories/{id}', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'updateCategory']);
        Route::delete('/categories/{id}', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'deleteCategory']);
        Route::put('/categories/{id}/status', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'updateCategoryStatus']);
        Route::get('/products', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'getMerchantProducts']);
        Route::get('/orders', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'getMerchantOrders']);
        Route::put('/merchants/{id}/audit', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'auditMerchant']);
        Route::put('/merchants/{id}/status', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'updateMerchantStatus']);
        Route::put('/products/{id}/audit', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'auditMerchantProduct']);
        Route::put('/products/{id}/status', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'updateProductStatus']);
        Route::post('/products/batch-audit', [\App\Http\Controllers\Admin\Api\V1\Mall\MerchantMallController::class, 'batchAuditProducts']);
    });

    // 商品分类管理（保留原有功能）
    Route::prefix('mall/categories')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'index']);
        Route::get('/list', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'list']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'store']);
        Route::get('/all', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'all']);
        Route::post('/batch-destroy', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'batchDestroy']);
        Route::post('/update-sort', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'updateSort']);
        Route::post('/upload-icon', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'uploadIcon']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'destroy']);
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\V1\MallCategoryController::class, 'updateStatus']);
    });

    // 商品管理（保留原有功能）
    Route::prefix('mall/products')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'store']);
        Route::get('/statistics', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'statistics']);
        Route::get('/categories', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'getCategories']);
        Route::get('/tags-statistics', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'getTagsStatistics']);
        Route::post('/batch-destroy', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'batchDestroy']);
        Route::post('/batch-operation', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'batchOperation']);
        Route::post('/batch-set-tags', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'batchSetTags']);
        Route::post('/upload-image', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'uploadImage']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'destroy']);
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'updateStatus']);
        Route::put('/{id}/on-sale', [\App\Http\Controllers\Admin\Api\V1\MallProductController::class, 'updateOnSale']);
    });

    // 订单管理（保留原有功能）
    Route::prefix('mall/orders')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\MallOrderController::class, 'index']);
        Route::get('/statistics', [\App\Http\Controllers\Admin\Api\V1\MallOrderController::class, 'statistics']);
        Route::get('/export', [\App\Http\Controllers\Admin\Api\V1\MallOrderController::class, 'export']);
        Route::post('/batch-ship', [\App\Http\Controllers\Admin\Api\V1\MallOrderController::class, 'batchShip']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\MallOrderController::class, 'show']);
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\V1\MallOrderController::class, 'updateStatus']);
        Route::post('/{id}/ship', [\App\Http\Controllers\Admin\Api\V1\MallOrderController::class, 'ship']);
        Route::post('/{id}/confirm', [\App\Http\Controllers\Admin\Api\V1\MallOrderController::class, 'confirm']);
        Route::post('/{id}/cancel', [\App\Http\Controllers\Admin\Api\V1\MallOrderController::class, 'cancel']);
        Route::put('/{id}/address', [\App\Http\Controllers\Admin\Api\V1\MallOrderController::class, 'updateAddress']);
    });

    // 生日祝福管理 - V1版本API
    Route::prefix('birthday')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'index']);
        Route::post('/send-sms', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'sendSms']);
        Route::post('/batch-send-sms', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'batchSendSms']);
        Route::get('/sms-logs', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'getSmsLogs']);
        Route::get('/statistics', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'getStatistics']);
        Route::get('/analytics', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'getAnalytics']);
        Route::get('/export-logs', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'exportLogs']);
        Route::get('/reminder-settings', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'getReminderSettings']);
        Route::post('/reminder-settings', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'updateReminderSettings']);
        Route::get('/send-history', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'getSendHistory']);
        Route::get('/upcoming-birthdays', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'getUpcomingBirthdays']);
        Route::post('/test-sms', [\App\Http\Controllers\Admin\Api\V1\BirthdayController::class, 'testSms']);
    });

    // 企业微信客户管理
    Route::prefix('wechat-work-customers')->withoutMiddleware([\App\Http\Middleware\LegacyTokenMiddleware::class])->group(function () {
        Route::get('/', [\App\Http\Controllers\Api\V1\WechatWorkCustomerController::class, 'index']);
        Route::get('/stats', [\App\Http\Controllers\Api\V1\WechatWorkCustomerController::class, 'getStats']);
        Route::get('/employees', [\App\Http\Controllers\Api\V1\WechatWorkCustomerController::class, 'getEmployees']);
        Route::post('/full-sync', [\App\Http\Controllers\Api\V1\WechatWorkCustomerController::class, 'fullSync']);
        Route::post('/clean-temp-customers', [\App\Http\Controllers\Api\V1\WechatWorkCustomerController::class, 'cleanTempCustomers']);
        Route::delete('/{id}', [\App\Http\Controllers\Api\V1\WechatWorkCustomerController::class, 'deleteCustomer']);
        Route::post('/batch-delete', [\App\Http\Controllers\Api\V1\WechatWorkCustomerController::class, 'batchDelete']);
    });

    // 系统管理
    Route::prefix('system')->group(function () {
        Route::get('info', [SystemController::class, 'info']);
        Route::get('config', [SystemController::class, 'getConfig']);
        Route::post('config', [SystemController::class, 'updateConfig']);
        Route::get('logs', [SystemController::class, 'getLogs']);
        Route::delete('logs', [SystemController::class, 'clearLogs']);
        Route::get('cache', [SystemController::class, 'getCacheInfo']);
        Route::delete('cache', [SystemController::class, 'clearCache']);
        Route::get('database', [SystemController::class, 'getDatabaseInfo']);
        Route::post('backup', [SystemController::class, 'createBackup']);
        Route::get('backups', [SystemController::class, 'getBackups']);
        Route::delete('backups/{filename}', [SystemController::class, 'deleteBackup']);
        Route::post('restore/{filename}', [SystemController::class, 'restoreBackup']);
    });

    // API接口管理 - V1版本API
    Route::prefix('api-management')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\ApiManagementController::class, 'index']);
        Route::post('/test', [\App\Http\Controllers\Admin\Api\V1\ApiManagementController::class, 'testApi']);
        Route::get('/detail', [\App\Http\Controllers\Admin\Api\V1\ApiManagementController::class, 'getApiDetail']);
        Route::get('/stats', [\App\Http\Controllers\Admin\Api\V1\ApiManagementController::class, 'getApiStats']);
    });

    // 分支机构菜单树获取 - 公开API（用于前端显示）
    Route::get('branch-menus/tree/{branchId?}', [BranchMenuController::class, 'getBranchMenus']);
    
    // VIP余额管理 - V1版本API（使用LegacyTokenMiddleware认证）
    Route::prefix('vip-balance')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\VipBalanceController::class, 'index']);
        Route::get('/statistics', [\App\Http\Controllers\Admin\Api\V1\VipBalanceController::class, 'statistics']);
        Route::post('/adjust', [\App\Http\Controllers\Admin\Api\V1\VipBalanceController::class, 'adjustBalance']);
        Route::get('/logs', [\App\Http\Controllers\Admin\Api\V1\VipBalanceController::class, 'balanceLogs']);
    });
});

// 分支机构管理后台路由 - 不需要认证（已在LegacyTokenMiddleware中处理）
Route::prefix('branch-organizations/{branchId}')->group(function () {
    // 管理员管理 - 不需要额外认证
    Route::get('/admins', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getAdmins']);
    Route::post('/admins', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'createAdmin']);
    Route::put('/admins/{adminId}', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'updateAdmin']);
    Route::put('/admins/{adminId}/status', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'toggleAdminStatus']);
    Route::post('/admins/{adminId}/reset-password', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'resetAdminPassword']);
    
    // VIP用户管理 - 不需要额外认证（分支机构管理员登录后即可访问）
    Route::get('/vip-users', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getVipUsers']);
    Route::get('/vip-team-members', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getVipTeamMembers']);
});

// 分支机构管理后台路由 - 其他功能需要认证和访问控制
Route::middleware(['auth:sanctum', 'admin.access'])->prefix('branch-organizations/{branchId}')->group(function () {
    // 注意：统计数据和活动API已在下方的BranchOrganizationController中定义，这里不重复定义
    
    // 角色管理
    Route::get('/roles', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getRoles']);
    Route::post('/roles', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'createRole']);
    Route::get('/roles/{roleId}', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getRole']);
    Route::put('/roles/{roleId}', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'updateRole']);
    Route::delete('/roles/{roleId}', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'deleteRole']);
    
    // 权限管理
    Route::get('/permissions', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getPermissions']);
    
    // APP用户管理 - 添加权限控制
    Route::get('/app-users', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getAppUsers']);
    Route::put('/app-users/{userId}', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'updateAppUser']);
    Route::put('/app-users/{userId}/status', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'toggleAppUserStatus']);
    

    
    // 业务员管理
    Route::get('/salesman', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getSalesman']);
    Route::post('/salesman', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'createSalesman']);
    Route::put('/salesman/{salesmanId}', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'updateSalesman']);
    Route::delete('/salesman/{salesmanId}', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'deleteSalesman']);
    Route::put('/salesman/{salesmanId}/status', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'toggleSalesmanStatus']);
    
    // 设备管理
    Route::get('/devices/inventory', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getDeviceInventory']);
    Route::get('/devices/activated', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getActivatedDevices']);
    
    // 分红配置 - 已移至公共路径区域，避免重复定义
    // Route::get('/dividend-config', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getDividendConfig']);
    // Route::put('/dividend-config', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'updateDividendConfig']);
    // Route::post('/dividend-config', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'saveDividendConfig']);
    
    // 微信公众号管理
    Route::get('/wechat/menu', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getWechatMenu']);
    Route::post('/wechat/menu', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'saveWechatMenu']);
    Route::post('/wechat/menu/publish', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'publishWechatMenu']);
    Route::delete('/wechat/menu', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'deleteWechatMenu']);
    Route::post('/wechat/menu/sync', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'syncWechatMenu']);
    Route::get('/wechat/menu/current', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getCurrentWechatMenu']);
    Route::post('/wechat/menu/copy-template', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'copyGlobalMenuTemplate']);
    Route::get('/wechat/auto-reply', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getAutoReply']);
    Route::post('/wechat/auto-reply', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'saveAutoReply']);

    // 分支机构系统配置
    Route::get('/system-config', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getSystemConfig']);
    Route::put('/system-config', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'saveSystemConfig']);


});

// 分支机构选项API - 不需要认证（用于前端下拉选择）
Route::get('branch-organizations/options', [BranchOrganizationController::class, 'options']);

// 分支机构管理 - V1版本API（不需要认证，修复"获取详情失败"问题）
Route::prefix('branch-organizations')->group(function () {
    Route::get('/', [BranchOrganizationController::class, 'index']);
    Route::post('/', [BranchOrganizationController::class, 'store']);
    Route::get('/{id}', [BranchOrganizationController::class, 'show']);
    Route::put('/{id}', [BranchOrganizationController::class, 'update']);
    Route::delete('/{id}', [BranchOrganizationController::class, 'destroy']);
    Route::put('/{id}/status', [BranchOrganizationController::class, 'updateStatus']);
});

// 分支机构统计和活动API - 不需要认证（用于仪表盘显示）
Route::get('branch-organizations/{id}/statistics', [BranchOrganizationController::class, 'statistics']);
Route::get('branch-organizations/{id}/activities', [BranchOrganizationController::class, 'activities']);
// 分支机构系统配置已移回需要认证的区域，避免重复路由
// Route::get('branch-organizations/{id}/system-config', [BranchOrganizationController::class, 'systemConfig']);

// 分支机构分红配置API - 不需要认证（修复"无效的令牌或已过期"问题）
Route::prefix('branch-organizations/{branchId}')->group(function () {
    Route::get('/dividend-config', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'getDividendConfig']);
    Route::put('/dividend-config', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'updateDividendConfig']);
    Route::post('/dividend-config', [App\Http\Controllers\Admin\Api\V1\BranchManagementController::class, 'saveDividendConfig']);
});

// 分支机构仪表盘API - 不需要认证（用于branch-standalone页面）
Route::prefix('branch')->group(function () {
    Route::prefix('dashboard')->group(function () {
        Route::get('stats', [BranchOrganizationController::class, 'dashboardStats']);
        Route::get('recent-users', [BranchOrganizationController::class, 'dashboardRecentUsers']);
        Route::get('device-status', [BranchOrganizationController::class, 'dashboardDeviceStatus']);
        Route::get('user-trend', [BranchOrganizationController::class, 'dashboardUserTrend']);
        Route::get('revenue-trend', [BranchOrganizationController::class, 'dashboardRevenueTrend']);
    });
    Route::get('notifications', [BranchOrganizationController::class, 'notifications']);
    Route::post('notifications/mark-all-read', [BranchOrganizationController::class, 'markNotificationsRead']);
});



// 需要认证的管理后台API
Route::middleware(['auth:sanctum', 'admin.access'])->group(function () {
    // 分支机构管理路由已移至不需要认证的区域

    // 分支机构信息API
    Route::prefix('branch')->group(function () {
        Route::get('/info', [BranchOrganizationController::class, 'getBranchByCode']);
    });

    // 分支机构菜单管理 - V1版本API
    Route::prefix('branch-menus')->group(function () {
        Route::get('/', [BranchMenuController::class, 'index']);
        Route::post('/', [BranchMenuController::class, 'store']);
        // 注意：tree/{branchId} 路由已在公共路径中定义，这里不重复定义
        Route::post('/initialize', [BranchMenuController::class, 'initializeBranchMenus']);
        Route::get('/parent-options', [BranchMenuController::class, 'getParentMenuOptions']);
        Route::get('/{id}', [BranchMenuController::class, 'show']);
        Route::put('/{id}', [BranchMenuController::class, 'update']);
        Route::put('/{id}/status', [BranchMenuController::class, 'updateStatus']);
        Route::delete('/{id}', [BranchMenuController::class, 'destroy']);
        Route::post('/batch-status', [BranchMenuController::class, 'batchUpdateStatus']);
        Route::post('/copy-default', [BranchMenuController::class, 'copyDefaultMenus']);
    });

    // APP用户管理 - 只保留存在的控制器
    Route::apiResource('app-users', \App\Http\Controllers\Admin\Api\V1\AppUserController::class);
    Route::put('app-users/{id}/status', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'updateStatus']);
    Route::post('app-users/{id}/reset-password', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'resetPassword']);
    Route::get('app-users/{id}/orders', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'orders']);
    Route::get('app-users/{id}/devices', [\App\Http\Controllers\Admin\Api\V1\AppUserController::class, 'devices']);
    
    // 后台管理员
    Route::apiResource('admins', \App\Http\Controllers\Admin\Api\V1\AdminController::class);
    Route::put('admins/{id}/status', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'updateStatus']);
    Route::post('admins/{id}/reset-password', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'resetPassword']);
    Route::get('admins/roles', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'roles']);
});